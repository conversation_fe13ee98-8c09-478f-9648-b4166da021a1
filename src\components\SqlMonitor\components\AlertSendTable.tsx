import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons';
import { Form, App, Drawer } from 'antd';
import React, { useCallback, useState, useRef } from 'react';

// 导入重构后的模块
import type { AlertSend, AlertSendSearchParams } from '@/components/SqlMonitor/types';
import { DEFAULT_PAGINATION } from '@/components/SqlMonitor/constants';
import { useAlertSendData, useDrawer } from '@/components/SqlMonitor/hooks';
import { useAlertSendTable } from '@/components/SqlMonitor/hooks/useAlertSendTable';
import { useSelectionCrossPage } from '@/components/SqlMonitor/hooks/useSelection';
import { tableStyles } from '@/components/SqlMonitor/styles';

// 导入拆分的组件
import { createAlertSendTableColumns } from '@/components/SqlMonitor/components/columns';
import { AlertSendActionButtons } from '@/components/SqlMonitor/components/button';
import { AlertSendTableComponent } from '@/components/SqlMonitor/components/table';
import { AlertSendBasicForm } from '@/components/SqlMonitor/components/forms';
import { AlertSendQuickSearchForm } from '@/components/SqlMonitor/components/forms';
import { AlertSendService } from '@/components/SqlMonitor/services/alertSend';

interface AlertSendTableProps {
  contentHeight?: number;
  isSelectionMode?: boolean;
  selectedRows?: AlertSend[];
  onSelectionConfirm?: (selectedAlertSends: AlertSend[]) => void;
}

/**
 * 告警发送管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AlertSendTable: React.FC<AlertSendTableProps> = ({ contentHeight, isSelectionMode = false, selectedRows = [], onSelectionConfirm }) => {
  const { message, modal } = App.useApp();

  // 使用自定义hooks管理状态
  const { data, loading, total, pagination, loadData, refreshData, updateSearchParams, updatePagination } = useAlertSendData({ autoLoad: true });

  const { rowSelectionAntdTable, clearSelection, getSelectedCount, getSelectedRows } = useSelectionCrossPage(data, {
    initialSelectedRows: isSelectionMode ? selectedRows : undefined,
    onSelectionChange: (_selectedKeys: React.Key[], rows: AlertSend[]) => {
      if (isSelectionMode && onSelectionConfirm) {
        onSelectionConfirm(rows);
      }
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } = useAlertSendTable({ contentHeight });

  // 抽屉状态管理
  const { drawerState: editDrawer, showDrawer: showEditDrawer, hideDrawer: hideEditDrawer } = useDrawer();

  // 当前编辑记录
  const [currentRecord, setCurrentRecord] = useState<AlertSend | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  // 表单实例
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // 组件卸载标志，用于防止在组件卸载后执行状态更新
  const isUnmountedRef = useRef(false);

  // useEffect(() => {
  //   return () => {
  //     console.log('AlertSendTable 组件卸载，开始执行清理操作');

  //     // 设置卸载标志
  //     isUnmountedRef.current = true;

  //     try {
  //       // 1. 清理表单状态
  //       searchForm.resetFields();
  //       if (editForm) {
  //         editForm.resetFields();
  //       }
  //       console.log('✓ 表单状态已清理');

  //       // 2. 清理选择状态
  //       clearSelection();
  //       console.log('✓ 选择状态已清理');

  //       // 3. 清理抽屉状态
  //       hideEditDrawer();
  //       console.log('✓ 抽屉状态已清理');

  //       // 4. 重置组件内部状态
  //       setCurrentRecord(null);
  //       setSubmitLoading(false);
  //       console.log('✓ 组件内部状态已重置');

  //       // 5. 清理可能的异步操作
  //       // 注意：由于使用的是 axios，无法直接取消请求
  //       // 但可以通过 isUnmountedRef 标志防止在组件卸载后更新状态
  //       console.log('✓ 异步操作清理标志已设置');

  //       console.log('AlertSendTable 组件清理完成');
  //     } catch (error) {
  //       console.error('组件清理过程中发生错误:', error);
  //     }
  //   };
  // }, [searchForm, editForm, clearSelection, hideEditDrawer]);

  // 分页变化处理
  const handlePaginationChange = (page: number, pageSize: number) => {
    // 检查组件是否已卸载
    if (isUnmountedRef.current) {
      console.log('组件已卸载，跳过分页变化处理');
      return;
    }

    console.log('分页变化:', { page, pageSize });
    updatePagination(page, pageSize);
    loadData({
      current: page,
      page_size: pageSize,
    });
  };

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: AlertSendSearchParams) => {
      // 检查组件是否已卸载
      if (isUnmountedRef.current) {
        console.log('组件已卸载，跳过搜索表单提交处理');
        return;
      }

      console.log('搜索参数:', values);
      updateSearchParams(values);
      updatePagination(1, pagination.page_size);
      loadData({
        ...values,
        current: 1,
        page_size: pagination.page_size,
      });
    },
    [updateSearchParams, updatePagination, pagination.page_size, loadData]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    // 检查组件是否已卸载
    if (isUnmountedRef.current) {
      console.log('组件已卸载，跳过重置搜索处理');
      return;
    }

    // 重置表单和其他状态，但不立即清空数据避免闪烁
    searchForm.resetFields();
    resetSortAndFilter();
    clearSelection();

    // 重置搜索参数和分页状态
    updateSearchParams({});
    updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);

    // 重新加载数据，明确传递空的搜索参数以确保重置生效
    loadData({
      current: DEFAULT_PAGINATION.current,
      page_size: DEFAULT_PAGINATION.page_size,
      // 明确传递空的搜索参数，覆盖可能还未更新的状态
      name: undefined,
      receive_type: undefined,
    });
  }, [searchForm, resetSortAndFilter, clearSelection, updateSearchParams, updatePagination, loadData]);

  // 编辑处理
  const handleEdit = useCallback(
    (record: AlertSend) => {
      console.log('编辑告警发送:', record);
      setCurrentRecord(record);
      editForm.setFieldsValue(record);
      showEditDrawer({
        visible: true,
        title: '编辑告警发送',
        width: '60%',
      });
    },
    [editForm, showEditDrawer]
  );

  // 删除处理
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        console.log('删除告警发送:', id);
        await AlertSendService.deleteAlertSend(id);

        // 检查组件是否已卸载
        if (isUnmountedRef.current) {
          console.log('组件已卸载，跳过状态更新');
          return;
        }

        message.success('删除成功');
        await refreshData();
        clearSelection();
      } catch (error) {
        console.error('删除失败:', error);

        // 检查组件是否已卸载
        if (!isUnmountedRef.current) {
          message.error('删除失败');
        }
      }
    },
    [message, refreshData, clearSelection]
  );

  // 批量删除处理
  const handleBatchDelete = useCallback(() => {
    const selectedRows = getSelectedRows();
    if (selectedRows.length === 0) {
      message.warning('请先选择要删除的数据');
      return;
    }

    modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRows.length} 条告警发送配置吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const deletePromises = selectedRows.map(row => AlertSendService.deleteAlertSend(row.id));
          await Promise.all(deletePromises);

          // 检查组件是否已卸载
          if (isUnmountedRef.current) {
            console.log('组件已卸载，跳过批量删除后的状态更新');
            return;
          }

          message.success(`成功删除 ${selectedRows.length} 条记录`);
          await refreshData();
          clearSelection();
        } catch (error) {
          console.error('批量删除失败:', error);

          // 检查组件是否已卸载
          if (!isUnmountedRef.current) {
            message.error('批量删除失败');
          }
        }
      },
    });
  }, [getSelectedRows, message, refreshData, clearSelection, modal]);

  // 表单提交处理
  const handleFormSubmit = useCallback(
    async (values: AlertSend) => {
      setSubmitLoading(true);
      try {
        console.log('提交告警发送表单:', values);

        if (currentRecord) {
          // 编辑模式
          await AlertSendService.updateAlertSend(values);

          // 检查组件是否已卸载
          if (isUnmountedRef.current) {
            console.log('组件已卸载，跳过更新成功后的状态更新');
            return;
          }

          message.success('更新成功');
        } else {
          // 新增模式
          await AlertSendService.addAlertSend(values);

          // 检查组件是否已卸载
          if (isUnmountedRef.current) {
            console.log('组件已卸载，跳过新增成功后的状态更新');
            return;
          }

          message.success('新增成功');
        }

        hideEditDrawer();
        editForm.resetFields();
        setCurrentRecord(null);
        await refreshData();
      } catch (error) {
        console.error('提交失败:', error);

        // 检查组件是否已卸载
        if (!isUnmountedRef.current) {
          message.error('提交失败');
        }
      } finally {
        // 检查组件是否已卸载
        if (!isUnmountedRef.current) {
          setSubmitLoading(false);
        }
      }
    },
    [currentRecord, message, hideEditDrawer, editForm, refreshData]
  );

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    // 重置表单数据
    editForm.resetFields();
    // 清空当前记录
    setCurrentRecord(null);
    // 重置提交状态
    setSubmitLoading(false);
    // 隐藏抽屉
    hideEditDrawer();
  }, [hideEditDrawer, editForm]);

  // 取消编辑
  const handleCancel = useCallback(() => {
    // 重置表单数据
    editForm.resetFields();
    // 清空当前记录
    setCurrentRecord(null);
    // 重置提交状态
    setSubmitLoading(false);
    // 隐藏抽屉
    hideEditDrawer();
  }, [hideEditDrawer, editForm]);

  // 表格列定义
  const columns = createAlertSendTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <AlertSendQuickSearchForm form={searchForm} onSubmit={handleSearchFormSubmit} onReset={handleReset} />

        {/* 操作按钮区域 */}
        <AlertSendActionButtons
          selectedCount={getSelectedCount()}
          onAddAlertSend={() => {
            setCurrentRecord(null);
            editForm.resetFields();
            showEditDrawer({
              visible: true,
              title: '新增告警发送',
              width: '60%',
            });
          }}
          onBatchDelete={handleBatchDelete}
          onClearSelection={() => {
            clearSelection();
            // 在选择模式下，通知父组件选择已清空
            if (isSelectionMode && onSelectionConfirm) {
              onSelectionConfirm([]);
            }
          }}
          isSelectionMode={isSelectionMode}
        />

        {/* 表格主体区域 */}
        <AlertSendTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelectionAntdTable}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={handlePaginationChange}
        />
      </div>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑告警发送' : '新增告警发送'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <AlertSendBasicForm
          form={editForm}
          initialData={currentRecord || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleCancel}
          onReset={() => {
            editForm.resetFields();
            message.info('表单已重置');
          }}
          loading={submitLoading}
        />
      </Drawer>
    </div>
  );
};

export default AlertSendTable;
